package com.gofore.aita.tender.data;

import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.tender.domain.models.WorkflowStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.gofore.aita.tender.domain.models.WorkflowStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface TenderRepository extends MongoRepository<Tender, String>, TenderRepositoryCustom {
  boolean existsByExternalId(String externalId);

    Page<Tender> findAllByWorkflowStatusInAndTitleContainingIgnoreCase(List<WorkflowStatus> workflowStatuses, String searchString, Pageable pageable);

}
